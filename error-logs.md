#CONTRACTS FOR SYMBOL(DERIV API EXPLORER)
{
  "contracts_for": "R_50",
  "currency": "USD",
  "landing_company": "svg",
  "product_type": "basic",
  "req_id": 5
}
{
  "contracts_for": {
    "available": [
      {
        "barrier_category": "american",
        "barriers": 2,
        "contract_category": "accumulator",
        "contract_category_display": "Accumulator",
        "contract_display": "Accumulator Up",
        "contract_type": "ACCU",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "no_expiry",
        "growth_rate_range": [
          0.01,
          0.02,
          0.03,
          0.04,
          0.05
        ],
        "high_barrier": "+0.0000",
        "low_barrier": "+0.0000",
        "market": "synthetic_index",
        "max_contract_duration": "0",
        "min_contract_duration": "0",
        "sentiment": "low_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "asian",
        "barriers": 0,
        "contract_category": "asian",
        "contract_category_display": "Asians",
        "contract_display": "Asian Up",
        "contract_type": "ASIANU",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "5t",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "asian",
        "barriers": 0,
        "contract_category": "asian",
        "contract_category_display": "Asians",
        "contract_display": "Asian Down",
        "contract_type": "ASIAND",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "5t",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Higher",
        "contract_type": "CALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Lower",
        "contract_type": "PUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "135.8468",
        "barrier_category": "euro_non_atm",
        "barriers": 1,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Higher",
        "contract_type": "CALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "135.8468",
        "barrier_category": "euro_non_atm",
        "barriers": 1,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Lower",
        "contract_type": "PUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Higher",
        "contract_type": "CALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "forward_starting_options": [
          {
            "close": "**********",
            "date": "**********",
            "open": "**********"
          },
          {
            "close": "1755129599",
            "date": "1755043200",
            "open": "1755043200"
          },
          {
            "close": "1755215999",
            "date": "1755129600",
            "open": "1755129600"
          }
        ],
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "2m",
        "sentiment": "up",
        "start_type": "forward",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Lower",
        "contract_type": "PUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "forward_starting_options": [
          {
            "close": "**********",
            "date": "**********",
            "open": "**********"
          },
          {
            "close": "1755129599",
            "date": "1755043200",
            "open": "1755043200"
          },
          {
            "close": "1755215999",
            "date": "1755129600",
            "open": "1755129600"
          }
        ],
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "2m",
        "sentiment": "down",
        "start_type": "forward",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Higher",
        "contract_type": "CALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "15s",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Lower",
        "contract_type": "PUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "15s",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0386",
        "barrier_category": "euro_non_atm",
        "barriers": 1,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Higher",
        "contract_type": "CALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "15s",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0386",
        "barrier_category": "euro_non_atm",
        "barriers": 1,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Lower",
        "contract_type": "PUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "15s",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Higher",
        "contract_type": "CALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "1t",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Lower",
        "contract_type": "PUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "1t",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0223",
        "barrier_category": "euro_non_atm",
        "barriers": 1,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Higher",
        "contract_type": "CALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "5t",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0223",
        "barrier_category": "euro_non_atm",
        "barriers": 1,
        "contract_category": "callput",
        "contract_category_display": "Up/Down",
        "contract_display": "Lower",
        "contract_type": "PUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "5t",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callputequal",
        "contract_category_display": "Rise/Fall Equal",
        "contract_display": "Higher",
        "contract_type": "CALLE",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callputequal",
        "contract_category_display": "Rise/Fall Equal",
        "contract_display": "Lower",
        "contract_type": "PUTE",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callputequal",
        "contract_category_display": "Rise/Fall Equal",
        "contract_display": "Higher",
        "contract_type": "CALLE",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "forward_starting_options": [
          {
            "close": "**********",
            "date": "**********",
            "open": "**********"
          },
          {
            "close": "1755129599",
            "date": "1755043200",
            "open": "1755043200"
          },
          {
            "close": "1755215999",
            "date": "1755129600",
            "open": "1755129600"
          }
        ],
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "2m",
        "sentiment": "up",
        "start_type": "forward",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callputequal",
        "contract_category_display": "Rise/Fall Equal",
        "contract_display": "Lower",
        "contract_type": "PUTE",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "forward_starting_options": [
          {
            "close": "**********",
            "date": "**********",
            "open": "**********"
          },
          {
            "close": "1755129599",
            "date": "1755043200",
            "open": "1755043200"
          },
          {
            "close": "1755215999",
            "date": "1755129600",
            "open": "1755129600"
          }
        ],
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "2m",
        "sentiment": "down",
        "start_type": "forward",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callputequal",
        "contract_category_display": "Rise/Fall Equal",
        "contract_display": "Higher",
        "contract_type": "CALLE",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "15s",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callputequal",
        "contract_category_display": "Rise/Fall Equal",
        "contract_display": "Lower",
        "contract_type": "PUTE",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "15s",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callputequal",
        "contract_category_display": "Rise/Fall Equal",
        "contract_display": "Higher",
        "contract_type": "CALLE",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "1t",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "callputequal",
        "contract_category_display": "Rise/Fall Equal",
        "contract_display": "Lower",
        "contract_type": "PUTE",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "1t",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "non_financial",
        "barriers": 1,
        "contract_category": "digits",
        "contract_category_display": "Digits",
        "contract_display": "Digit Matches",
        "contract_type": "DIGITMATCH",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "last_digit_range": [
          0,
          1,
          2,
          3,
          4,
          5,
          6,
          7,
          8,
          9
        ],
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "1t",
        "sentiment": "match",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "non_financial",
        "barriers": 1,
        "contract_category": "digits",
        "contract_category_display": "Digits",
        "contract_display": "Digit Differs",
        "contract_type": "DIGITDIFF",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "last_digit_range": [
          0,
          1,
          2,
          3,
          4,
          5,
          6,
          7,
          8,
          9
        ],
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "1t",
        "sentiment": "differ",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "non_financial",
        "barriers": 0,
        "contract_category": "digits",
        "contract_category_display": "Digits",
        "contract_display": "Digit Odd",
        "contract_type": "DIGITODD",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "1t",
        "sentiment": "odd",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "non_financial",
        "barriers": 0,
        "contract_category": "digits",
        "contract_category_display": "Digits",
        "contract_display": "Digit Even",
        "contract_type": "DIGITEVEN",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "1t",
        "sentiment": "even",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "non_financial",
        "barriers": 1,
        "contract_category": "digits",
        "contract_category_display": "Digits",
        "contract_display": "Digit Over",
        "contract_type": "DIGITOVER",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "last_digit_range": [
          0,
          1,
          2,
          3,
          4,
          5,
          6,
          7,
          8
        ],
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "1t",
        "sentiment": "over",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "non_financial",
        "barriers": 1,
        "contract_category": "digits",
        "contract_category_display": "Digits",
        "contract_display": "Digit Under",
        "contract_type": "DIGITUNDER",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "last_digit_range": [
          1,
          2,
          3,
          4,
          5,
          6,
          7,
          8,
          9
        ],
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "1t",
        "sentiment": "under",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_non_atm",
        "barriers": 2,
        "contract_category": "endsinout",
        "contract_category_display": "Ends Between/Ends Outside",
        "contract_display": "Ends Outside",
        "contract_type": "EXPIRYMISS",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "high_barrier": "135.8468",
        "low_barrier": "129.9923",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "high_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_non_atm",
        "barriers": 2,
        "contract_category": "endsinout",
        "contract_category_display": "Ends Between/Ends Outside",
        "contract_display": "Ends Between",
        "contract_type": "EXPIRYRANGE",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "high_barrier": "135.8468",
        "low_barrier": "129.9923",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "low_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_non_atm",
        "barriers": 2,
        "contract_category": "endsinout",
        "contract_category_display": "Ends Between/Ends Outside",
        "contract_display": "Ends Outside",
        "contract_type": "EXPIRYMISS",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "high_barrier": "+0.1092",
        "low_barrier": "-0.1089",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "2m",
        "sentiment": "high_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_non_atm",
        "barriers": 2,
        "contract_category": "endsinout",
        "contract_category_display": "Ends Between/Ends Outside",
        "contract_display": "Ends Between",
        "contract_type": "EXPIRYRANGE",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "high_barrier": "+0.1092",
        "low_barrier": "-0.1089",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "2m",
        "sentiment": "low_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "american",
        "barriers": 0,
        "contract_category": "highlowticks",
        "contract_category_display": "High/Low Ticks",
        "contract_display": "High Tick",
        "contract_type": "TICKHIGH",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "5t",
        "min_contract_duration": "5t",
        "sentiment": "high",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "american",
        "barriers": 0,
        "contract_category": "highlowticks",
        "contract_category_display": "High/Low Ticks",
        "contract_display": "Low Tick",
        "contract_type": "TICKLOW",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "5t",
        "min_contract_duration": "5t",
        "sentiment": "low",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "lookback",
        "barriers": 0,
        "contract_category": "lookback",
        "contract_category_display": "Lookbacks",
        "contract_display": "Close-Low",
        "contract_type": "LBFLOATCALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "30m",
        "min_contract_duration": "1m",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "lookback",
        "barriers": 0,
        "contract_category": "lookback",
        "contract_category_display": "Lookbacks",
        "contract_display": "High-Close",
        "contract_type": "LBFLOATPUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "30m",
        "min_contract_duration": "1m",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "lookback",
        "barriers": 0,
        "contract_category": "lookback",
        "contract_category_display": "Lookbacks",
        "contract_display": "High-Low",
        "contract_type": "LBHIGHLOW",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "30m",
        "min_contract_duration": "1m",
        "sentiment": "updown",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "american",
        "barriers": 0,
        "cancellation_range": [
          "5m",
          "10m",
          "15m",
          "30m",
          "60m"
        ],
        "contract_category": "multiplier",
        "contract_category_display": "Multiply Up/Multiply Down",
        "contract_display": "Multiply Up",
        "contract_type": "MULTUP",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "no_expiry",
        "market": "synthetic_index",
        "max_contract_duration": "0",
        "min_contract_duration": "0",
        "multiplier_range": [
          20,
          200,
          300,
          500,
          800
        ],
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "american",
        "barriers": 0,
        "cancellation_range": [
          "5m",
          "10m",
          "15m",
          "30m",
          "60m"
        ],
        "contract_category": "multiplier",
        "contract_category_display": "Multiply Up/Multiply Down",
        "contract_display": "Multiply Down",
        "contract_type": "MULTDOWN",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "no_expiry",
        "market": "synthetic_index",
        "max_contract_duration": "0",
        "min_contract_duration": "0",
        "multiplier_range": [
          20,
          200,
          300,
          500,
          800
        ],
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0445",
        "barrier_category": "reset",
        "barriers": 1,
        "contract_category": "reset",
        "contract_category_display": "Reset Call/Reset Put",
        "contract_display": "Reset Call",
        "contract_type": "RESETCALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "2h",
        "min_contract_duration": "20s",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0445",
        "barrier_category": "reset",
        "barriers": 1,
        "contract_category": "reset",
        "contract_category_display": "Reset Call/Reset Put",
        "contract_display": "Reset Put",
        "contract_type": "RESETPUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "2h",
        "min_contract_duration": "20s",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0223",
        "barrier_category": "reset",
        "barriers": 1,
        "contract_category": "reset",
        "contract_category_display": "Reset Call/Reset Put",
        "contract_display": "Reset Call",
        "contract_type": "RESETCALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "5t",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0223",
        "barrier_category": "reset",
        "barriers": 1,
        "contract_category": "reset",
        "contract_category_display": "Reset Call/Reset Put",
        "contract_display": "Reset Put",
        "contract_type": "RESETPUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "5t",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0141",
        "barrier_category": "american",
        "barriers": 1,
        "contract_category": "runs",
        "contract_category_display": "Only Ups/Only Downs",
        "contract_display": "Only Ups",
        "contract_type": "RUNHIGH",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "5t",
        "min_contract_duration": "2t",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0141",
        "barrier_category": "american",
        "barriers": 1,
        "contract_category": "runs",
        "contract_category_display": "Only Ups/Only Downs",
        "contract_display": "Only Downs",
        "contract_type": "RUNLOW",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "5t",
        "min_contract_duration": "2t",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "american",
        "barriers": 2,
        "contract_category": "staysinout",
        "contract_category_display": "Stays Between/Goes Outside",
        "contract_display": "Stays Between",
        "contract_type": "RANGE",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "high_barrier": "135.8468",
        "low_barrier": "129.9923",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "low_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "american",
        "barriers": 2,
        "contract_category": "staysinout",
        "contract_category_display": "Stays Between/Goes Outside",
        "contract_display": "Goes Outside",
        "contract_type": "UPORDOWN",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "high_barrier": "135.8468",
        "low_barrier": "129.9923",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "high_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "american",
        "barriers": 2,
        "contract_category": "staysinout",
        "contract_category_display": "Stays Between/Goes Outside",
        "contract_display": "Stays Between",
        "contract_type": "RANGE",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "high_barrier": "+0.1092",
        "low_barrier": "-0.1089",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "2m",
        "sentiment": "low_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "american",
        "barriers": 2,
        "contract_category": "staysinout",
        "contract_category_display": "Stays Between/Goes Outside",
        "contract_display": "Goes Outside",
        "contract_type": "UPORDOWN",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "high_barrier": "+0.1092",
        "low_barrier": "-0.1089",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "2m",
        "sentiment": "high_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "135.8468",
        "barrier_category": "american",
        "barriers": 1,
        "contract_category": "touchnotouch",
        "contract_category_display": "Touch/No Touch",
        "contract_display": "Touches",
        "contract_type": "ONETOUCH",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "high_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "135.8468",
        "barrier_category": "american",
        "barriers": 1,
        "contract_category": "touchnotouch",
        "contract_category_display": "Touch/No Touch",
        "contract_display": "Does Not Touch",
        "contract_type": "NOTOUCH",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "low_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.1092",
        "barrier_category": "american",
        "barriers": 1,
        "contract_category": "touchnotouch",
        "contract_category_display": "Touch/No Touch",
        "contract_display": "Touches",
        "contract_type": "ONETOUCH",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "2m",
        "sentiment": "high_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.1092",
        "barrier_category": "american",
        "barriers": 1,
        "contract_category": "touchnotouch",
        "contract_category_display": "Touch/No Touch",
        "contract_display": "Does Not Touch",
        "contract_type": "NOTOUCH",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "2m",
        "sentiment": "low_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0223",
        "barrier_category": "american",
        "barriers": 1,
        "contract_category": "touchnotouch",
        "contract_category_display": "Touch/No Touch",
        "contract_display": "Touches",
        "contract_type": "ONETOUCH",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "5t",
        "sentiment": "high_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0223",
        "barrier_category": "american",
        "barriers": 1,
        "contract_category": "touchnotouch",
        "contract_category_display": "Touch/No Touch",
        "contract_display": "Does Not Touch",
        "contract_type": "NOTOUCH",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "min_contract_duration": "5t",
        "sentiment": "low_vol",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "5.0860",
        "barrier_category": "american",
        "barrier_choices": [
          "0.2049",
          "0.3895",
          "0.7404",
          "1.4074",
          "2.6755",
          "5.0860",
          "9.6685",
          "18.3798",
          "34.9400",
          "66.4209"
        ],
        "barriers": 1,
        "contract_category": "turbos",
        "contract_category_display": "Turbos Options",
        "contract_display": "Turbos Long",
        "contract_type": "TURBOSLONG",
        "default_stake": 10,
        "display_number_of_contracts": 24,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "max_stake": 15314.46,
        "min_contract_duration": "1d",
        "min_stake": 0.77,
        "payout_choices": [
          40,
          32,
          24,
          16,
          8
        ],
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "5.0860",
        "barrier_category": "american",
        "barrier_choices": [
          "0.2049",
          "0.3895",
          "0.7404",
          "1.4074",
          "2.6755",
          "5.0860",
          "9.6685",
          "18.3798",
          "34.9400",
          "66.4209"
        ],
        "barriers": 1,
        "contract_category": "turbos",
        "contract_category_display": "Turbos Options",
        "contract_display": "Turbos Short",
        "contract_type": "TURBOSSHORT",
        "default_stake": 10,
        "display_number_of_contracts": 24,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "max_stake": 15314.46,
        "min_contract_duration": "1d",
        "min_stake": 0.77,
        "payout_choices": [
          40,
          32,
          24,
          16,
          8
        ],
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "5.0860",
        "barrier_category": "american",
        "barrier_choices": [
          "0.2049",
          "0.3895",
          "0.7404",
          "1.4074",
          "2.6755",
          "5.0860",
          "9.6685",
          "18.3798",
          "34.9400",
          "66.4209"
        ],
        "barriers": 1,
        "contract_category": "turbos",
        "contract_category_display": "Turbos Options",
        "contract_display": "Turbos Long",
        "contract_type": "TURBOSLONG",
        "default_stake": 10,
        "display_number_of_contracts": 24,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "max_stake": 15314.46,
        "min_contract_duration": "15s",
        "min_stake": 0.77,
        "payout_choices": [
          40,
          32,
          24,
          16,
          8
        ],
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "5.0860",
        "barrier_category": "american",
        "barrier_choices": [
          "0.2049",
          "0.3895",
          "0.7404",
          "1.4074",
          "2.6755",
          "5.0860",
          "9.6685",
          "18.3798",
          "34.9400",
          "66.4209"
        ],
        "barriers": 1,
        "contract_category": "turbos",
        "contract_category_display": "Turbos Options",
        "contract_display": "Turbos Short",
        "contract_type": "TURBOSSHORT",
        "default_stake": 10,
        "display_number_of_contracts": 24,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "max_stake": 15314.46,
        "min_contract_duration": "15s",
        "min_stake": 0.77,
        "payout_choices": [
          40,
          32,
          24,
          16,
          8
        ],
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "5.0860",
        "barrier_category": "american",
        "barrier_choices": [
          "0.2049",
          "0.3895",
          "0.7404",
          "1.4074",
          "2.6755",
          "5.0860",
          "9.6685",
          "18.3798",
          "34.9400",
          "66.4209"
        ],
        "barriers": 1,
        "contract_category": "turbos",
        "contract_category_display": "Turbos Options",
        "contract_display": "Turbos Long",
        "contract_type": "TURBOSLONG",
        "default_stake": 10,
        "display_number_of_contracts": 24,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "max_stake": 15314.46,
        "min_contract_duration": "5t",
        "min_stake": 0.77,
        "payout_choices": [
          40,
          32,
          24,
          16,
          8
        ],
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "5.0860",
        "barrier_category": "american",
        "barrier_choices": [
          "0.2049",
          "0.3895",
          "0.7404",
          "1.4074",
          "2.6755",
          "5.0860",
          "9.6685",
          "18.3798",
          "34.9400",
          "66.4209"
        ],
        "barriers": 1,
        "contract_category": "turbos",
        "contract_category_display": "Turbos Options",
        "contract_display": "Turbos Short",
        "contract_type": "TURBOSSHORT",
        "default_stake": 10,
        "display_number_of_contracts": 24,
        "exchange_name": "RANDOM",
        "expiry_type": "tick",
        "market": "synthetic_index",
        "max_contract_duration": "10t",
        "max_stake": 15314.46,
        "min_contract_duration": "5t",
        "min_stake": 0.77,
        "payout_choices": [
          40,
          32,
          24,
          16,
          8
        ],
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "vanilla",
        "contract_category_display": "Vanilla Options",
        "contract_display": "Vanilla Long Call",
        "contract_type": "VANILLALONGCALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "vanilla",
        "contract_category_display": "Vanilla Options",
        "contract_display": "Vanilla Long Put",
        "contract_type": "VANILLALONGPUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "133.0000",
        "barrier_category": "euro_non_atm",
        "barrier_choices": [
          "129.0000",
          "133.0000",
          "137.0000"
        ],
        "barriers": 1,
        "contract_category": "vanilla",
        "contract_category_display": "Vanilla Options",
        "contract_display": "Vanilla Long Call",
        "contract_type": "VANILLALONGCALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "133.0000",
        "barrier_category": "euro_non_atm",
        "barrier_choices": [
          "129.0000",
          "133.0000",
          "137.0000"
        ],
        "barriers": 1,
        "contract_category": "vanilla",
        "contract_category_display": "Vanilla Options",
        "contract_display": "Vanilla Long Put",
        "contract_type": "VANILLALONGPUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "daily",
        "market": "synthetic_index",
        "max_contract_duration": "365d",
        "min_contract_duration": "1d",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "vanilla",
        "contract_category_display": "Vanilla Options",
        "contract_display": "Vanilla Long Call",
        "contract_type": "VANILLALONGCALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "1m",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier_category": "euro_atm",
        "barriers": 0,
        "contract_category": "vanilla",
        "contract_category_display": "Vanilla Options",
        "contract_display": "Vanilla Long Put",
        "contract_type": "VANILLALONGPUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "1m",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0000",
        "barrier_category": "euro_non_atm",
        "barrier_choices": [
          "+0.1180",
          "+0.0620",
          "+0.0000",
          "-0.0620",
          "-0.1170"
        ],
        "barriers": 1,
        "contract_category": "vanilla",
        "contract_category_display": "Vanilla Options",
        "contract_display": "Vanilla Long Call",
        "contract_type": "VANILLALONGCALL",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "1m",
        "sentiment": "up",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      },
      {
        "barrier": "+0.0000",
        "barrier_category": "euro_non_atm",
        "barrier_choices": [
          "+0.1180",
          "+0.0620",
          "+0.0000",
          "-0.0620",
          "-0.1170"
        ],
        "barriers": 1,
        "contract_category": "vanilla",
        "contract_category_display": "Vanilla Options",
        "contract_display": "Vanilla Long Put",
        "contract_type": "VANILLALONGPUT",
        "default_stake": 10,
        "exchange_name": "RANDOM",
        "expiry_type": "intraday",
        "market": "synthetic_index",
        "max_contract_duration": "1d",
        "min_contract_duration": "1m",
        "sentiment": "down",
        "start_type": "spot",
        "submarket": "random_index",
        "underlying_symbol": "R_50"
      }
    ],
    "close": **********,
    "feed_license": "realtime",
    "hit_count": 72,
    "non_available": [],
    "open": **********,
    "spot": 132.8418
  },
  "echo_req": {
    "contracts_for": "R_50",
    "currency": "USD",
    "landing_company": "svg",
    "product_type": "basic",
    "req_id": 5
  },
  "msg_type": "contracts_for",
  "req_id": 5
}