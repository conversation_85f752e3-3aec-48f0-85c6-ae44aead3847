2025-08-12T11:10:05.294Z [info] [ProfitTable] WebSocket connected
2025-08-12T11:10:05.606Z [error] [ProfitTable] Error syncing profit table: Error [PrismaClientValidationError]: 
Invalid `prisma.profitTableEntry.createMany()` invocation:

{
  data: [
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 350,
      contractId: 290829493648n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 677,
      purchaseTime: 1754996955n,
      sellPrice: 677,
      sellTime: 1754996956n,
      shortcode: "DIGITEVEN_1HZ75V_6.77_1754996955_1T",
      transactionId: 579433469988n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 350,
      contractId: 290829493628n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 677,
      purchaseTime: 1754996955n,
      sellPrice: 677,
      sellTime: 1754996956n,
      shortcode: "DIGITEVEN_1HZ75V_6.77_1754996955_1T",
      transactionId: 579433469968n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 350,
      contractId: 290829493548n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 677,
      purchaseTime: 1754996955n,
      sellPrice: 677,
      sellTime: 1754996956n,
      shortcode: "DIGITEVEN_1HZ75V_6.77_1754996955_1T",
      transactionId: 579433469828n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 350,
      contractId: 290829493528n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 677,
      purchaseTime: 1754996955n,
      sellPrice: 677,
      sellTime: 1754996956n,
      shortcode: "DIGITEVEN_1HZ75V_6.77_1754996955_1T",
      transactionId: 579433469788n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434308n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342898088n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434288n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342898028n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434268n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342898068n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434228n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897948n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434208n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897988n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434188n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897868n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434148n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897908n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434128n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897968n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434108n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897828n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434088n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897808n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783383808n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949141n,
      sellPrice: 1845,
      sellTime: 1754949143n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949141_1T",
      transactionId: 579342797708n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783383428n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949141n,
      sellPrice: 1845,
      sellTime: 1754949143n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949141_1T",
      transactionId: 579342797188n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783383188n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949141n,
      sellPrice: 1845,
      sellTime: 1754949142n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949141_1T",
      transactionId: 579342796788n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783382868n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949140n,
      sellPrice: 1845,
      sellTime: 1754949142n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949140_1T",
      transactionId: 579342796268n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783382188n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949140n,
      sellPrice: 1845,
      sellTime: 1754949142n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949140_1T",
      transactionId: 579342795348n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783381508n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949140n,
      sellPrice: 1845,
      sellTime: 1754949142n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949140_1T",
      transactionId: 579342794048n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783381348n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949139n,
      sellPrice: null,
      sellTime: 1754949141n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949139_1T",
      transactionId: 579342793428n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783381028n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949139n,
      sellPrice: null,
      sellTime: 1754949141n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949139_1T",
      transactionId: 579342793028n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783380768n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949139n,
      sellPrice: null,
      sellTime: 1754949141n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949139_1T",
      transactionId: 579342792588n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783380708n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949139n,
      sellPrice: null,
      sellTime: 1754949140n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949139_1T",
      transactionId: 579342792308n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 1000,
      contractId: 290782794588n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is odd after 1 ticks.",
      payout: 1934,
      purchaseTime: 1754948615n,
      sellPrice: null,
      sellTime: 1754948616n,
      shortcode: "DIGITODD_1HZ100V_19.34_1754948615_1T",
      transactionId: 579341642748n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780422848n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946672n,
      sellPrice: null,
      sellTime: 1754946676n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946672_1T",
      transactionId: 579337082808n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780421668n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946672n,
      sellPrice: null,
      sellTime: 1754946674n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946672_1T",
      transactionId: 579337080428n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780420708n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946671n,
      sellPrice: null,
      sellTime: 1754946674n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946671_1T",
      transactionId: 579337078808n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780417068n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946667n,
      sellPrice: null,
      sellTime: 1754946670n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946667_1T",
      transactionId: 579337071448n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780416128n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946667n,
      sellPrice: null,
      sellTime: 1754946670n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946667_1T",
      transactionId: 579337069728n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780414908n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946666n,
      sellPrice: null,
      sellTime: 1754946670n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946666_1T",
      transactionId: 579337066648n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780414108n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946665n,
      sellPrice: null,
      sellTime: 1754946668n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946665_1T",
      transactionId: 579337064828n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780412788n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946664n,
      sellPrice: null,
      sellTime: 1754946668n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946664_1T",
      transactionId: 579337061968n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780411388n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946663n,
      sellPrice: null,
      sellTime: 1754946666n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946663_1T",
      transactionId: 579337059908n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780410308n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946662n,
      sellPrice: null,
      sellTime: 1754946666n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946662_1T",
      transactionId: 579337057108n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780409288n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946661n,
      sellPrice: 209,
      sellTime: 1754946664n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946661_1T",
      transactionId: 579337055028n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780408008n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946660n,
      sellPrice: 209,
      sellTime: 1754946664n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946660_1T",
      transactionId: 579337052188n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010408n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946337n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336280008n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010368n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279828n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010348n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279848n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010328n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279808n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010308n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279768n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010288n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279748n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010228n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279728n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010208n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279648n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010188n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279628n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010168n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279588n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779800348n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754946167n,
      sellPrice: null,
      sellTime: 1754946169n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754946167_1T",
      transactionId: 579335865368n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779800068n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754946167n,
      sellPrice: null,
      sellTime: 1754946169n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754946167_1T",
      transactionId: 579335865008n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779800008n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754946167n,
      sellPrice: null,
      sellTime: 1754946169n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754946167_1T",
      transactionId: 579335864928n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779799988n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754946167n,
      sellPrice: null,
      sellTime: 1754946169n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754946167_1T",
      transactionId: 579335864908n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779799948n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754946167n,
      sellPrice: null,
      sellTime: 1754946169n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754946167_1T",
      transactionId: 579335864888n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779799928n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754946167n,
      sellPrice: null,
      sellTime: 1754946169n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754946167_1T",
      transactionId: 579335864828n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779488928n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754945933n,
      sellPrice: 323,
      sellTime: 1754945935n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754945933_1T",
      transactionId: 579335250368n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779488908n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754945933n,
      sellPrice: 323,
      sellTime: 1754945935n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754945933_1T",
      transactionId: 579335250328n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779488868n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754945933n,
      sellPrice: 323,
      sellTime: 1754945935n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754945933_1T",
      transactionId: 579335250268n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779488848n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754945933n,
      sellPrice: 323,
      sellTime: 1754945935n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754945933_1T",
      transactionId: 579335250208n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779488728n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754945933n,
      sellPrice: 323,
      sellTime: 1754945935n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754945933_1T",
      transactionId: 579335250028n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779488588n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754945933n,
      sellPrice: 323,
      sellTime: 1754945935n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754945933_1T",
      transactionId: 579335249868n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 333,
      contractId: ************n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is odd after 1 ticks.",
      payout: 644,
      purchaseTime: 1754945809n,
      sellPrice: null,
      sellTime: 1754945811n,
      shortcode: "DIGITODD_1HZ10V_6.44_1754945809_1T",
      transactionId: 579334920388n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 333,
      contractId: 290779321388n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is odd after 1 ticks.",
      payout: 644,
      purchaseTime: 1754945809n,
      sellPrice: null,
      sellTime: 1754945811n,
      shortcode: "DIGITODD_1HZ10V_6.44_1754945809_1T",
      transactionId: 579334919448n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 333,
      contractId: 290779321368n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is odd after 1 ticks.",
      payout: 644,
      purchaseTime: 1754945809n,
      sellPrice: null,
      sellTime: 1754945811n,
      shortcode: "DIGITODD_1HZ10V_6.44_1754945809_1T",
      transactionId: 579334919408n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316908n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944311n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949648n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316848n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944311n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949528n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316828n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944310n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949508n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316768n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944310n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949368n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316748n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944311n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949448n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316728n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944310n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949388n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316708n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944309n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949348n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 200,
      contractId: 290772562448n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 387,
      purchaseTime: 1754941035n,
      sellPrice: null,
      sellTime: 1754941036n,
      shortcode: "DIGITODD_1HZ75V_3.87_1754941035_1T",
      transactionId: 579321581548n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 200,
      contractId: 290772562368n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 387,
      purchaseTime: 1754941035n,
      sellPrice: null,
      sellTime: 1754941036n,
      shortcode: "DIGITODD_1HZ75V_3.87_1754941035_1T",
      transactionId: 579321581488n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 200,
      contractId: 290772562348n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 387,
      purchaseTime: 1754941035n,
      sellPrice: null,
      sellTime: 1754941036n,
      shortcode: "DIGITODD_1HZ75V_3.87_1754941035_1T",
      transactionId: 579321581448n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 200,
      contractId: 290772562328n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 387,
      purchaseTime: 1754941035n,
      sellPrice: null,
      sellTime: 1754941036n,
      shortcode: "DIGITODD_1HZ75V_3.87_1754941035_1T",
      transactionId: 579321581428n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 200,
      contractId: 290772562288n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 387,
      purchaseTime: 1754941035n,
      sellPrice: null,
      sellTime: 1754941036n,
      shortcode: "DIGITODD_1HZ75V_3.87_1754941035_1T",
      transactionId: 579321581388n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704948n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939096n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315931328n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704928n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939097n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315931268n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704908n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939097n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315931148n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704868n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939097n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315931228n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704728n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939096n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315930808n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704688n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939097n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315930728n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704648n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939097n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315930588n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 250,
      contractId: 290769067628n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is even after 1 ticks.",
      payout: 484,
      purchaseTime: 1754938676n,
      sellPrice: 484,
      sellTime: 1754938679n,
      shortcode: "DIGITEVEN_1HZ100V_4.84_1754938676_1T",
      transactionId: 579314674228n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 250,
      contractId: 290769067448n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is even after 1 ticks.",
      payout: 484,
      purchaseTime: 1754938676n,
      sellPrice: 484,
      sellTime: 1754938678n,
      shortcode: "DIGITEVEN_1HZ100V_4.84_1754938676_1T",
      transactionId: 579314673848n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 250,
      contractId: 290769067208n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is even after 1 ticks.",
      payout: 484,
      purchaseTime: 1754938676n,
      sellPrice: 484,
      sellTime: 1754938678n,
      shortcode: "DIGITEVEN_1HZ100V_4.84_1754938676_1T",
      transactionId: 579314673188n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 250,
      contractId: 290769067188n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is even after 1 ticks.",
      payout: 484,
      purchaseTime: 1754938676n,
      sellPrice: 484,
      sellTime: 1754938679n,
      shortcode: "DIGITEVEN_1HZ100V_4.84_1754938676_1T",
      transactionId: 579314673148n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 250,
      contractId: 290769067168n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is even after 1 ticks.",
      payout: 484,
      purchaseTime: 1754938676n,
      sellPrice: 484,
      sellTime: 1754938678n,
      shortcode: "DIGITEVEN_1HZ100V_4.84_1754938676_1T",
      transactionId: 579314673168n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 250,
      contractId: 290769067148n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is even after 1 ticks.",
      payout: 484,
      purchaseTime: 1754938676n,
      sellPrice: 484,
      sellTime: 1754938678n,
      shortcode: "DIGITEVEN_1HZ100V_4.84_1754938676_1T",
      transactionId: 579314673128n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 260,
      contractId: 290741605888n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is odd after 1 ticks.",
      payout: 503,
      purchaseTime: 1754920267n,
      sellPrice: 503,
      sellTime: 1754920269n,
      shortcode: "DIGITODD_1HZ100V_5.03_1754920267_1T",
      transactionId: 579260730648n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 260,
      contractId: 290741604368n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is odd after 1 ticks.",
      payout: 503,
      purchaseTime: 1754920266n,
      sellPrice: null,
      sellTime: 1754920268n,
      shortcode: "DIGITODD_1HZ100V_5.03_1754920266_1T",
      transactionId: 579260727628n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 260,
      contractId: 290741604328n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is odd after 1 ticks.",
      payout: 503,
      purchaseTime: 1754920266n,
      sellPrice: null,
      sellTime: 1754920268n,
      shortcode: "DIGITODD_1HZ100V_5.03_1754920266_1T",
      transactionId: 579260727508n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 260,
      contractId: 290741604308n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is odd after 1 ticks.",
      payout: 503,
      purchaseTime: 1754920266n,
      sellPrice: null,
      sellTime: 1754920268n,
      shortcode: "DIGITODD_1HZ100V_5.03_1754920266_1T",
      transactionId: 579260727468n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 260,
      contractId: 290741604288n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is odd after 1 ticks.",
      payout: 503,
      purchaseTime: 1754920266n,
      sellPrice: null,
      sellTime: 1754920268n,
      shortcode: "DIGITODD_1HZ100V_5.03_1754920266_1T",
      transactionId: 579260727348n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738829148n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918452n,
      sellPrice: 331,
      sellTime: 1754918454n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918452_1T",
      transactionId: 579255442328n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738827928n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918451n,
      sellPrice: 331,
      sellTime: 1754918453n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918451_1T",
      transactionId: 579255439748n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738823568n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918448n,
      sellPrice: null,
      sellTime: 1754918450n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918448_1T",
      transactionId: 579255430828n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738822668n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918447n,
      sellPrice: null,
      sellTime: 1754918449n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918447_1T",
      transactionId: 579255429168n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738821148n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918446n,
      sellPrice: null,
      sellTime: 1754918448n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918446_1T",
      transactionId: 579255425908n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738819688n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918445n,
      sellPrice: 331,
      sellTime: 1754918447n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918445_1T",
      transactionId: 579255423448n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738817628n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918444n,
      sellPrice: null,
      sellTime: 1754918446n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918444_1T",
      transactionId: 579255420248n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290736149748n,
      longcode: "Win payout if the last digit of Volatility 50 (1s) Index is odd after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754916761n,
      sellPrice: 1845,
      sellTime: 1754916763n,
      shortcode: "DIGITODD_1HZ50V_18.45_1754916761_1T",
      transactionId: 579250353928n,
      symbol: "1HZ50V",
      profit: null,
      durationType: "ticks",
      duration: null
    }
  ],
  skipDuplicates: true
}

Unknown argument `duration`. Available options are marked with ?.
    at async d.storeProfitTableEntries (.next/server/app/api/deriv/sync-profit-table/route.js:1:3379)
    at async d.syncProfitTable (.next/server/app/api/deriv/sync-profit-table/route.js:1:3717)
    at async h (.next/server/app/api/deriv/sync-profit-table/route.js:1:4825) {
  clientVersion: '6.13.0'
}
2025-08-12T11:10:05.614Z [error] [API] Error syncing profit table: Error [PrismaClientValidationError]: 
Invalid `prisma.profitTableEntry.createMany()` invocation:

{
  data: [
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 350,
      contractId: 290829493648n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 677,
      purchaseTime: 1754996955n,
      sellPrice: 677,
      sellTime: 1754996956n,
      shortcode: "DIGITEVEN_1HZ75V_6.77_1754996955_1T",
      transactionId: 579433469988n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 350,
      contractId: 290829493628n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 677,
      purchaseTime: 1754996955n,
      sellPrice: 677,
      sellTime: 1754996956n,
      shortcode: "DIGITEVEN_1HZ75V_6.77_1754996955_1T",
      transactionId: 579433469968n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 350,
      contractId: 290829493548n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 677,
      purchaseTime: 1754996955n,
      sellPrice: 677,
      sellTime: 1754996956n,
      shortcode: "DIGITEVEN_1HZ75V_6.77_1754996955_1T",
      transactionId: 579433469828n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 350,
      contractId: 290829493528n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 677,
      purchaseTime: 1754996955n,
      sellPrice: 677,
      sellTime: 1754996956n,
      shortcode: "DIGITEVEN_1HZ75V_6.77_1754996955_1T",
      transactionId: 579433469788n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434308n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342898088n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434288n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342898028n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434268n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342898068n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434228n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897948n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434208n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897988n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434188n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897868n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434148n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897908n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434128n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897968n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434108n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897828n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783434088n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949185n,
      sellPrice: 1845,
      sellTime: 1754949187n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949185_1T",
      transactionId: 579342897808n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783383808n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949141n,
      sellPrice: 1845,
      sellTime: 1754949143n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949141_1T",
      transactionId: 579342797708n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783383428n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949141n,
      sellPrice: 1845,
      sellTime: 1754949143n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949141_1T",
      transactionId: 579342797188n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783383188n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949141n,
      sellPrice: 1845,
      sellTime: 1754949142n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949141_1T",
      transactionId: 579342796788n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783382868n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949140n,
      sellPrice: 1845,
      sellTime: 1754949142n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949140_1T",
      transactionId: 579342796268n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783382188n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949140n,
      sellPrice: 1845,
      sellTime: 1754949142n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949140_1T",
      transactionId: 579342795348n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783381508n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949140n,
      sellPrice: 1845,
      sellTime: 1754949142n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949140_1T",
      transactionId: 579342794048n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783381348n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949139n,
      sellPrice: null,
      sellTime: 1754949141n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949139_1T",
      transactionId: 579342793428n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783381028n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949139n,
      sellPrice: null,
      sellTime: 1754949141n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949139_1T",
      transactionId: 579342793028n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783380768n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949139n,
      sellPrice: null,
      sellTime: 1754949141n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949139_1T",
      transactionId: 579342792588n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290783380708n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754949139n,
      sellPrice: null,
      sellTime: 1754949140n,
      shortcode: "DIGITEVEN_1HZ10V_18.45_1754949139_1T",
      transactionId: 579342792308n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 1000,
      contractId: 290782794588n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is odd after 1 ticks.",
      payout: 1934,
      purchaseTime: 1754948615n,
      sellPrice: null,
      sellTime: 1754948616n,
      shortcode: "DIGITODD_1HZ100V_19.34_1754948615_1T",
      transactionId: 579341642748n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780422848n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946672n,
      sellPrice: null,
      sellTime: 1754946676n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946672_1T",
      transactionId: 579337082808n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780421668n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946672n,
      sellPrice: null,
      sellTime: 1754946674n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946672_1T",
      transactionId: 579337080428n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780420708n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946671n,
      sellPrice: null,
      sellTime: 1754946674n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946671_1T",
      transactionId: 579337078808n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780417068n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946667n,
      sellPrice: null,
      sellTime: 1754946670n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946667_1T",
      transactionId: 579337071448n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780416128n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946667n,
      sellPrice: null,
      sellTime: 1754946670n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946667_1T",
      transactionId: 579337069728n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780414908n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946666n,
      sellPrice: null,
      sellTime: 1754946670n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946666_1T",
      transactionId: 579337066648n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780414108n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946665n,
      sellPrice: null,
      sellTime: 1754946668n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946665_1T",
      transactionId: 579337064828n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780412788n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946664n,
      sellPrice: null,
      sellTime: 1754946668n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946664_1T",
      transactionId: 579337061968n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780411388n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946663n,
      sellPrice: null,
      sellTime: 1754946666n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946663_1T",
      transactionId: 579337059908n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780410308n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946662n,
      sellPrice: null,
      sellTime: 1754946666n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946662_1T",
      transactionId: 579337057108n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780409288n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946661n,
      sellPrice: 209,
      sellTime: 1754946664n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946661_1T",
      transactionId: 579337055028n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 108,
      contractId: 290780408008n,
      longcode: "Win payout if the last digit of Volatility 25 Index is even after 1 ticks.",
      payout: 209,
      purchaseTime: 1754946660n,
      sellPrice: 209,
      sellTime: 1754946664n,
      shortcode: "DIGITEVEN_R_25_2.09_1754946660_1T",
      transactionId: 579337052188n,
      symbol: "R_25",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010408n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946337n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336280008n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010368n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279828n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010348n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279848n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010328n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279808n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010308n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279768n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010288n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279748n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010228n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279728n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010208n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279648n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010188n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279628n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 130,
      contractId: 290780010168n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is even after 1 ticks.",
      payout: 251,
      purchaseTime: 1754946335n,
      sellPrice: null,
      sellTime: 1754946336n,
      shortcode: "DIGITEVEN_1HZ10V_2.51_1754946335_1T",
      transactionId: 579336279588n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779800348n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754946167n,
      sellPrice: null,
      sellTime: 1754946169n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754946167_1T",
      transactionId: 579335865368n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779800068n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754946167n,
      sellPrice: null,
      sellTime: 1754946169n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754946167_1T",
      transactionId: 579335865008n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779800008n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754946167n,
      sellPrice: null,
      sellTime: 1754946169n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754946167_1T",
      transactionId: 579335864928n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779799988n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754946167n,
      sellPrice: null,
      sellTime: 1754946169n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754946167_1T",
      transactionId: 579335864908n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779799948n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754946167n,
      sellPrice: null,
      sellTime: 1754946169n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754946167_1T",
      transactionId: 579335864888n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779799928n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754946167n,
      sellPrice: null,
      sellTime: 1754946169n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754946167_1T",
      transactionId: 579335864828n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779488928n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754945933n,
      sellPrice: 323,
      sellTime: 1754945935n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754945933_1T",
      transactionId: 579335250368n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779488908n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754945933n,
      sellPrice: 323,
      sellTime: 1754945935n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754945933_1T",
      transactionId: 579335250328n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779488868n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754945933n,
      sellPrice: 323,
      sellTime: 1754945935n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754945933_1T",
      transactionId: 579335250268n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779488848n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754945933n,
      sellPrice: 323,
      sellTime: 1754945935n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754945933_1T",
      transactionId: 579335250208n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779488728n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754945933n,
      sellPrice: 323,
      sellTime: 1754945935n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754945933_1T",
      transactionId: 579335250028n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 167,
      contractId: 290779488588n,
      longcode: "Win payout if the last digit of Volatility 25 (1s) Index is odd after 1 ticks.",
      payout: 323,
      purchaseTime: 1754945933n,
      sellPrice: 323,
      sellTime: 1754945935n,
      shortcode: "DIGITODD_1HZ25V_3.23_1754945933_1T",
      transactionId: 579335249868n,
      symbol: "1HZ25V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 333,
      contractId: ************n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is odd after 1 ticks.",
      payout: 644,
      purchaseTime: 1754945809n,
      sellPrice: null,
      sellTime: 1754945811n,
      shortcode: "DIGITODD_1HZ10V_6.44_1754945809_1T",
      transactionId: 579334920388n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 333,
      contractId: 290779321388n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is odd after 1 ticks.",
      payout: 644,
      purchaseTime: 1754945809n,
      sellPrice: null,
      sellTime: 1754945811n,
      shortcode: "DIGITODD_1HZ10V_6.44_1754945809_1T",
      transactionId: 579334919448n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 333,
      contractId: 290779321368n,
      longcode: "Win payout if the last digit of Volatility 10 (1s) Index is odd after 1 ticks.",
      payout: 644,
      purchaseTime: 1754945809n,
      sellPrice: null,
      sellTime: 1754945811n,
      shortcode: "DIGITODD_1HZ10V_6.44_1754945809_1T",
      transactionId: 579334919408n,
      symbol: "1HZ10V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316908n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944311n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949648n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316848n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944311n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949528n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316828n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944310n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949508n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316768n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944310n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949368n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316748n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944311n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949448n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316728n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944310n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949388n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 143,
      contractId: 290777316708n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 277,
      purchaseTime: 1754944308n,
      sellPrice: null,
      sellTime: 1754944309n,
      shortcode: "DIGITODD_1HZ75V_2.77_1754944308_1T",
      transactionId: 579330949348n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 200,
      contractId: 290772562448n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 387,
      purchaseTime: 1754941035n,
      sellPrice: null,
      sellTime: 1754941036n,
      shortcode: "DIGITODD_1HZ75V_3.87_1754941035_1T",
      transactionId: 579321581548n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 200,
      contractId: 290772562368n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 387,
      purchaseTime: 1754941035n,
      sellPrice: null,
      sellTime: 1754941036n,
      shortcode: "DIGITODD_1HZ75V_3.87_1754941035_1T",
      transactionId: 579321581488n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 200,
      contractId: 290772562348n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 387,
      purchaseTime: 1754941035n,
      sellPrice: null,
      sellTime: 1754941036n,
      shortcode: "DIGITODD_1HZ75V_3.87_1754941035_1T",
      transactionId: 579321581448n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 200,
      contractId: 290772562328n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 387,
      purchaseTime: 1754941035n,
      sellPrice: null,
      sellTime: 1754941036n,
      shortcode: "DIGITODD_1HZ75V_3.87_1754941035_1T",
      transactionId: 579321581428n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 200,
      contractId: 290772562288n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 387,
      purchaseTime: 1754941035n,
      sellPrice: null,
      sellTime: 1754941036n,
      shortcode: "DIGITODD_1HZ75V_3.87_1754941035_1T",
      transactionId: 579321581388n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704948n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939096n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315931328n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704928n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939097n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315931268n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704908n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939097n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315931148n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704868n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939097n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315931228n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704728n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939096n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315930808n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704688n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939097n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315930728n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 186,
      contractId: 290769704648n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is odd after 1 ticks.",
      payout: 360,
      purchaseTime: 1754939094n,
      sellPrice: null,
      sellTime: 1754939097n,
      shortcode: "DIGITODD_1HZ75V_3.60_1754939094_1T",
      transactionId: 579315930588n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 250,
      contractId: 290769067628n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is even after 1 ticks.",
      payout: 484,
      purchaseTime: 1754938676n,
      sellPrice: 484,
      sellTime: 1754938679n,
      shortcode: "DIGITEVEN_1HZ100V_4.84_1754938676_1T",
      transactionId: 579314674228n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 250,
      contractId: 290769067448n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is even after 1 ticks.",
      payout: 484,
      purchaseTime: 1754938676n,
      sellPrice: 484,
      sellTime: 1754938678n,
      shortcode: "DIGITEVEN_1HZ100V_4.84_1754938676_1T",
      transactionId: 579314673848n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 250,
      contractId: 290769067208n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is even after 1 ticks.",
      payout: 484,
      purchaseTime: 1754938676n,
      sellPrice: 484,
      sellTime: 1754938678n,
      shortcode: "DIGITEVEN_1HZ100V_4.84_1754938676_1T",
      transactionId: 579314673188n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 250,
      contractId: 290769067188n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is even after 1 ticks.",
      payout: 484,
      purchaseTime: 1754938676n,
      sellPrice: 484,
      sellTime: 1754938679n,
      shortcode: "DIGITEVEN_1HZ100V_4.84_1754938676_1T",
      transactionId: 579314673148n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 250,
      contractId: 290769067168n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is even after 1 ticks.",
      payout: 484,
      purchaseTime: 1754938676n,
      sellPrice: 484,
      sellTime: 1754938678n,
      shortcode: "DIGITEVEN_1HZ100V_4.84_1754938676_1T",
      transactionId: 579314673168n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 250,
      contractId: 290769067148n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is even after 1 ticks.",
      payout: 484,
      purchaseTime: 1754938676n,
      sellPrice: 484,
      sellTime: 1754938678n,
      shortcode: "DIGITEVEN_1HZ100V_4.84_1754938676_1T",
      transactionId: 579314673128n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 260,
      contractId: 290741605888n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is odd after 1 ticks.",
      payout: 503,
      purchaseTime: 1754920267n,
      sellPrice: 503,
      sellTime: 1754920269n,
      shortcode: "DIGITODD_1HZ100V_5.03_1754920267_1T",
      transactionId: 579260730648n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 260,
      contractId: 290741604368n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is odd after 1 ticks.",
      payout: 503,
      purchaseTime: 1754920266n,
      sellPrice: null,
      sellTime: 1754920268n,
      shortcode: "DIGITODD_1HZ100V_5.03_1754920266_1T",
      transactionId: 579260727628n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 260,
      contractId: 290741604328n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is odd after 1 ticks.",
      payout: 503,
      purchaseTime: 1754920266n,
      sellPrice: null,
      sellTime: 1754920268n,
      shortcode: "DIGITODD_1HZ100V_5.03_1754920266_1T",
      transactionId: 579260727508n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 260,
      contractId: 290741604308n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is odd after 1 ticks.",
      payout: 503,
      purchaseTime: 1754920266n,
      sellPrice: null,
      sellTime: 1754920268n,
      shortcode: "DIGITODD_1HZ100V_5.03_1754920266_1T",
      transactionId: 579260727468n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 260,
      contractId: 290741604288n,
      longcode: "Win payout if the last digit of Volatility 100 (1s) Index is odd after 1 ticks.",
      payout: 503,
      purchaseTime: 1754920266n,
      sellPrice: null,
      sellTime: 1754920268n,
      shortcode: "DIGITODD_1HZ100V_5.03_1754920266_1T",
      transactionId: 579260727348n,
      symbol: "1HZ100V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738829148n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918452n,
      sellPrice: 331,
      sellTime: 1754918454n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918452_1T",
      transactionId: 579255442328n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738827928n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918451n,
      sellPrice: 331,
      sellTime: 1754918453n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918451_1T",
      transactionId: 579255439748n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738823568n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918448n,
      sellPrice: null,
      sellTime: 1754918450n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918448_1T",
      transactionId: 579255430828n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738822668n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918447n,
      sellPrice: null,
      sellTime: 1754918449n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918447_1T",
      transactionId: 579255429168n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738821148n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918446n,
      sellPrice: null,
      sellTime: 1754918448n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918446_1T",
      transactionId: 579255425908n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738819688n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918445n,
      sellPrice: 331,
      sellTime: 1754918447n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918445_1T",
      transactionId: 579255423448n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 80447,
      buyPrice: 171,
      contractId: 290738817628n,
      longcode: "Win payout if the last digit of Volatility 75 (1s) Index is even after 1 ticks.",
      payout: 331,
      purchaseTime: 1754918444n,
      sellPrice: null,
      sellTime: 1754918446n,
      shortcode: "DIGITEVEN_1HZ75V_3.31_1754918444_1T",
      transactionId: 579255420248n,
      symbol: "1HZ75V",
      profit: null,
      durationType: "ticks",
      duration: null
    },
    {
      userId: "********",
      derivAccountId: "VRTC13200397",
      accountType: "demo",
      appId: 37016,
      buyPrice: 1000,
      contractId: 290736149748n,
      longcode: "Win payout if the last digit of Volatility 50 (1s) Index is odd after 1 ticks.",
      payout: 1845,
      purchaseTime: 1754916761n,
      sellPrice: 1845,
      sellTime: 1754916763n,
      shortcode: "DIGITODD_1HZ50V_18.45_1754916761_1T",
      transactionId: 579250353928n,
      symbol: "1HZ50V",
      profit: null,
      durationType: "ticks",
      duration: null
    }
  ],
  skipDuplicates: true
}

Unknown argument `duration`. Available options are marked with ?.
    at async d.storeProfitTableEntries (.next/server/app/api/deriv/sync-profit-table/route.js:1:3379)
    at async d.syncProfitTable (.next/server/app/api/deriv/sync-profit-table/route.js:1:3717)
    at async h (.next/server/app/api/deriv/sync-profit-table/route.js:1:4825) {
  clientVersion: '6.13.0'
}