generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(cuid())
  userId       String
  expires      DateTime
  sessionToken String   @unique
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model User {
  id                String            @id @default(cuid())
  email             String?           @unique
  name              String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  googleId          String?           @unique
  picture           String?
  authMethod        String?
  displayName       String?
  avatarDataUrl     String?
  emailVerified     DateTime?
  image             String?
  hashedPassword    String?
  derivAccountId    String?           @unique
  provider          String?
  accounts          Account[]
  apiKeys           ApiKey[]
  notifications     Notification[]
  alerts            PriceAlert[]
  profitSummary     ProfitSummary?
  profitTableEntries ProfitTableEntry[]
  savedItems        SavedItem[]
  sessions          Session[]
  trades            Trade[]
  usageStats        UsageStats[]
  settings          UserSettings?
  watchlists        Watchlist[]
}

model UserSettings {
  id                       String    @id @default(uuid())
  userId                   String    @unique
  theme                    String    @default("light")
  language                 String    @default("en")
  notifications            Boolean   @default(true)
  settings                 Json
  derivDemoAccountId       String?
  derivRealAccountId       String?
  selectedDerivAccountType String?   @default("demo")
  derivDemoBalance         Float?
  derivRealBalance         Float?
  lastBalanceSync          DateTime?
  derivRealApiToken        String?
  derivDemoApiToken        String?
  createdAt                DateTime  @default(now())
  updatedAt                DateTime  @updatedAt
  user                     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model UsageStats {
  id        String   @id @default(uuid())
  userId    String
  action    String
  timestamp DateTime @default(now())
  metadata  Json?
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([timestamp])
}

model SavedItem {
  id        String   @id @default(uuid())
  userId    String
  title     String
  content   String
  url       String?
  tags      String[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([tags])
}

model Notification {
  id        String   @id @default(uuid())
  userId    String
  title     String
  message   String
  type      String
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([read])
}

model Trade {
  id                    String  @id @default(uuid())
  userId                String
  symbol                String
  status                String
  derivAccountId        String?
  accountType           String?
  derivLongcode         String?
  derivShortcode        String?
  derivBuyPrice         Int?
  derivPayout           Float?
  derivPurchaseTime     BigInt?
  derivSellPrice        Float?
  derivSellTime         BigInt?
  derivContractType     String?
  derivUnderlyingSymbol String?
  derivDurationType     String?
  derivAppId            Int?
  metadata              Json?
  derivContractId       BigInt?
  derivTransactionId    BigInt?
  user                  User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([symbol])
  @@index([status])
  @@index([derivContractId])
  @@index([derivAccountId])
  @@index([derivContractType])
  @@index([derivUnderlyingSymbol])
  @@index([derivPurchaseTime])
}

model ProfitSummary {
  id            String   @id @default(uuid())
  userId        String   @unique
  totalProfit   Float    @default(0)
  totalTrades   Int      @default(0)
  winningTrades Int      @default(0)
  losingTrades  Int      @default(0)
  winRate       Float    @default(0)
  lastUpdated   DateTime @updatedAt
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model ProfitTableEntry {
  id             String   @id @default(uuid())
  userId         String
  derivAccountId String
  accountType    String
  appId          Int
  buyPrice       Int      // Stored in cents
  contractId     BigInt
  longcode       String
  payout         Int      // Stored in cents
  purchaseTime   BigInt
  sellPrice      Int?     // Stored in cents
  sellTime       BigInt?
  shortcode      String
  transactionId  BigInt
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([contractId, derivAccountId])
  @@index([userId])
  @@index([derivAccountId])
  @@index([contractId])
  @@index([purchaseTime])
}

model Watchlist {
  id        String   @id @default(uuid())
  userId    String
  name      String
  symbols   String[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([symbols])
}

model PriceAlert {
  id        String   @id @default(uuid())
  userId    String
  symbol    String
  price     Float
  condition String
  triggered Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([symbol])
  @@index([triggered])
}

model ApiKey {
  id         String   @id @default(uuid())
  userId     String
  exchange   String
  apiKey     String
  secretKey  String
  passphrase String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([exchange])
}

model ExtensionLog {
  id        String   @id @default(uuid())
  level     String
  message   String
  metadata  Json?
  timestamp DateTime @default(now())

  @@index([level])
  @@index([timestamp])
}

model PasswordResetToken {
  id        String   @id @default(cuid())
  token     String   @unique
  email     String
  expires   DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email])
  @@index([token])
}
